--ESX = nil
ESX = exports["es_extended"]:getSharedObject()

--TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

-- Logs
local discord = {
    ['webhook'] = '',
    ['name'] = 'DrugBot',
    ['image'] = 'https://media.discordapp.net/attachments/832929996578226216/913995687178416148/istockphoto-979955616-612x612.jpg.png?width=676&height=676'
}

function Log(name, message)
    local data = {
        {
            ["color"] = '15277667',
            ["title"] = "**".. name .."**",
            ["description"] = "**".. message .."**",
        }
    }
    PerformHttpRequest(discord['webhook'], function(err, text, headers) end, 'POST', json.encode({username = discord['name'], embeds = data, avatar_url = discord['image']}), { ['Content-Type'] = 'application/json' })
end

-- Support old ESX btw idk why you dont want update :/
function showNotification(src, msg)
    TriggerClientEvent('esx:showNotification', src, msg)
end

-- thanks to loaf he made this snip long time ago so i didnt put time to read old esx to see how those work :D https://docs.loaf-scripts.com/
function canCarryItem(src, item, count)
    local xPlayer = ESX.GetPlayerFromId(src)
    if not count then count = 1 end
    local item = xPlayer.getInventoryItem(item)
    return ((item.weight == -1) or ((item.count + count) <= item.weight))
end



-- Get Enough Jobs Is in Server Or No
function HaveEnough(JobTable)
    local Have = true
    if ESX.GetExtendedPlayers then
        for k,v in pairs(JobTable) do
            local xPlayers = ESX.GetExtendedPlayers("job",k)
            if #xPlayers < v then
                Have = false
                break
            end
        end
    elseif ESX.GetPlayers then
        local xPlayers = ESX.GetPlayers()
        local Connected = {}
        for i=1, #xPlayers, 1 do
            local xPlayer = ESX.GetPlayerFromId(xPlayers[i])
            if JobTable[xPlayer.job.name] then
                if not Connected[xPlayer.job.name] then Connected[xPlayer.job.name] = 0 end
                Connected[xPlayer.job.name] = Connected[xPlayer.job.name] + 1
            end
        end
        for k,v in pairs(JobTable) do
            if not Connected[k] or Connected[k] < v then
                Have = false
                break
            end
        end
    else
        local Players = GetPlayers()
        local Connected = {}
        for i=1, #Players, 1 do
            local xPlayer = ESX.GetPlayerFromId(Players[i])
            if xPlayer and xPlayer.job and xPlayer.job.name then
                if JobTable[xPlayer.job.name] then
                    if not Connected[xPlayer.job.name] then Connected[xPlayer.job.name] = 0 end
                    Connected[xPlayer.job.name] = Connected[xPlayer.job.name] + 1
                end
            end
        end
        for k,v in pairs(JobTable) do
            if not Connected[k] or Connected[k] < v then
                Have = false
                break
            end
        end
    end
    return Have
end

-- Harvesting Event
RegisterServerEvent('lab-fields:harvest')
AddEventHandler('lab-fields:harvest', function(Index)
    local source = source

    local xPlayer = ESX.GetPlayerFromId(source)
    
	if xPlayer.job.name == 'police' then
		TriggerClientEvent('esx:showNotification', source, '警察禁止采集')
		return
	end
	if xPlayer.job.name == 'offpolice' then
		TriggerClientEvent('esx:showNotification', source, '警察禁止采集')
		return
	end
	if xPlayer.job.name == 'ambulance' then
		TriggerClientEvent('esx:showNotification', source, '医护禁止采集')
		return
	end
	if xPlayer.job.name == 'offambulance' then
		TriggerClientEvent('esx:showNotification', source, '医护禁止采集')
		return
	end


    local Field = Config.Fields[Index]

    if not xPlayer then return end
    if (not Field.jobs) or (Field.jobs[xPlayer.job.name] and Field.jobs[xPlayer.job.name] <= xPlayer.job.grade) then
        local IsThereEnoughJob = true
        local xPlayers = ESX.GetExtendedPlayers('job', 'police')
        local RequiredCopsWeed = #xPlayers
        
        if Field.neededJobs then
            IsThereEnoughJob = HaveEnough(Field.neededJobs)
        end
        if RequiredCopsWeed >= 0 then  --警察数量设置
        --if IsThereEnoughJob then
            local GivenItemName = Field.itemName
            math.randomseed(os.time() + math.random(os.time()) + math.random())
            math.random(); math.random(); math.random();
            local GivenItemCount = math.random(math.min(Field.amount.Min, Field.amount.Max),math.max(Field.amount.Min, Field.amount.Max))
            local Label = Field.label or ESX.GetItemLabel(GivenItemName)
            if xPlayer.canCarryItem(GivenItemName, GivenItemCount) then
                xPlayer.addInventoryItem(GivenItemName, GivenItemCount)
                showNotification(source, '你采集了' .. GivenItemCount .. '个' .. Label .. '!')
            else
                showNotification(source, '你的背包满了!')
			end
        else
            showNotification(source, '没有足够的警察在线.')
        end
    else
        showNotification(source, 'You cant dot this in this job you have.')
    end
end)


-- Processing Event
RegisterServerEvent('lab-fields:process')
AddEventHandler('lab-fields:process', function(Index)
    local source = source

    local xPlayer = ESX.GetPlayerFromId(source)
    
	if xPlayer.job.name == 'police' then
		TriggerClientEvent('esx:showNotification', source, '警察不能进行加工')
		return
	end
	if xPlayer.job.name == 'offpolice' then
		TriggerClientEvent('esx:showNotification', source, '警察不能进行加工')
		return
	end
	if xPlayer.job.name == 'ambulance' then
		TriggerClientEvent('esx:showNotification', source, '医护不能进行加工')
		return
	end
	if xPlayer.job.name == 'offambulance' then
		TriggerClientEvent('esx:showNotification', source, '医护不能进行加工')
		return
	end

    local Lab = Config.Labs[Index]

    if not xPlayer then return end
    if (not Lab.jobs) or (Lab.jobs[xPlayer.job.name] and Lab.jobs[xPlayer.job.name] <= xPlayer.job.grade) then
        local IsThereEnoughJob = true
        local RequiredCopsWeed = 0
        local xPlayers = ESX.GetPlayers()
        for i=1, #xPlayers, 1 do
            local xPlayer = ESX.GetPlayerFromId(xPlayers[i])
            if xPlayer.job.name == 'police' then
                RequiredCopsWeed = RequiredCopsWeed + 1
                end
        end
        if Lab.neededJobs then
            IsThereEnoughJob = HaveEnough(Lab.neededJobs)
        end
        --if IsThereEnoughJob then
        if RequiredCopsWeed >= 0 then  --警察数量设置
            local givenItem = Lab.givenItem
            math.randomseed(os.time() + math.random(os.time()) + math.random())
            math.random(); math.random(); math.random();
            local givenAmount = math.random(math.min(Lab.givenAmount.Min, Lab.givenAmount.Max),math.max(Lab.givenAmount.Min, Lab.givenAmount.Max))
            local givenLabel = Lab.givenLabel or ESX.GetItemLabel(givenItem)
            local neededItem = Lab.neededItem
			local neededItem1 = Lab.neededItem1
            local neededAmount = Lab.neededAmount
            local neededLabel = Lab.neededLabel
			local neededLabel1 = Lab.neededLabel1
            if xPlayer.getInventoryItem(neededItem) and xPlayer.getInventoryItem(neededItem1) and xPlayer.getInventoryItem(neededItem).count and xPlayer.getInventoryItem(neededItem1).count >= neededAmount and ((xPlayer.canCarryItem and xPlayer.canCarryItem(givenItem, givenAmount)) or canCarryItem(source, givenItem, givenAmount)) then
                xPlayer.removeInventoryItem(neededItem, neededAmount)
				xPlayer.removeInventoryItem(neededItem1, neededAmount)
                print(givenItem, givenAmount)
                xPlayer.addInventoryItem(givenItem, givenAmount)
                showNotification(source, '你把' .. neededAmount .. '个' .. neededLabel..'和'.. neededLabel1 .. ' 加工成' .. givenAmount .. '个' .. givenLabel ..'.')
              --  TriggerClientEvent("pNotify:SendNotification", source, {text = '<b>毒品</b></br>你把' .. neededAmount .. '个' .. neededLabel..'和'.. neededLabel1 .. ' 加工成 ' .. givenAmount .. '个' .. givenLabel ..'.', timeout = 3000})

            else
                showNotification(source, '材料不足，无法加工!')
              --  TriggerClientEvent("pNotify:SendNotification", source, {text = '<b>毒品</b></br>材料不足，无法加工!', timeout = 3000})
            end
        else
            showNotification(source, '没有足够的警察在线.')
          --  TriggerClientEvent("pNotify:SendNotification", source, {text = '<b>毒品</b></br>没有足够的警察在线!', timeout = 3000})
        end
    else
        showNotification(source, 'You cant dot this in this job you have.')
    end
end)