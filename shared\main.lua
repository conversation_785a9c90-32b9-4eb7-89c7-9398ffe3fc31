Config = {}

Config.BlipSize = 0.8 -- Size of the blips.

Config.PropOutline = true -- Enable if you want field props to be outlined when close.
Config.OutlineColor = {r = 42, g = 191, b = 171}

-- Configure anything except: spawnedPlants, DrugPlantsA and DrugCoords. These shall never be touched!
Config.Fields = {
    {
        FieldCoords = vector3(5585.73, -5222.95, 17.39),
        label = '可卡因', 
        itemName = 'coca', 
        amount = {Min = 1, Max = 1},
        jobs = false,
        neededJobs = false,
        DrugProp = 'prop_cs_plant_01',
        duration = 3000,
        animDict= 'random@domestic',
        anim = 'pickup_low',
        blip = true,
        blipSprite = 140,
        blipColour = 1,
        blipRadius = true,
        spawnedPlants = 0, DrugPlantsA = {}, DrugCoords = nil
    },
    {
        FieldCoords = vector3(5478, -5846.27978515625, 20.45000076293945),
        label = '丙酮',
        itemName = 'bingtong',
        amount = {Min = 1, Max = 1},
        jobs = false,
        neededJobs = false,
        DrugProp = 'prop_drug_bottle',
        duration = 6000,
        animDict= 'random@domestic',
        anim = 'pickup_low',
        blip = true,
        blipSprite = 499,
        blipColour = 1,
        blipRadius = true,
        spawnedPlants = 0, DrugPlantsA = {}, DrugCoords = nil
    },
    --[[{
        FieldCoords = vector3(4825.57, -5781.68, 36.25),
        label = '盐酸',
        itemName = 'yansuan',
        amount = {Min = 2, Max = 2},
        jobs = false,
        neededJobs = false,
        DrugProp = 'v_med_cor_chemical',
        duration = 6000,
        animDict= 'random@domestic',
        anim = 'pickup_low',
        blip = true,
        blipSprite = 499,
        blipColour = 1,
        blipRadius = true,
        spawnedPlants = 0, DrugPlantsA = {}, DrugCoords = nil
    },]]
	--[[{
        FieldCoords = vector3(2223.82, 5576.95, 53.84),
        label = '大麻', 
        itemName = 'dama', 
        amount = {Min = 2, Max = 2},
        jobs = false,
        neededJobs = false,
        DrugProp = 'prop_weed_02',
        duration = 6000,
        animDict= 'random@domestic',
        anim = 'pickup_low',
        blip = true,
        blipSprite = 496,
        blipColour = 1,
        blipRadius = true,
        spawnedPlants = 0, DrugPlantsA = {}, DrugCoords = nil
    },]]
}

-- Configure anything to your liking.
Config.Labs = {
   {
        LabCoords = vector3(5067.26, -4591.43, 2.86),
        neededLabel = '丙酮',
		neededLabel1 = '丙酮',
        givenLabel = '冰毒',
        neededItem = 'bingtong',
        neededItem1 = 'bingtong',
        neededAmount = 2,
        givenItem = 'bingdu',
        givenAmount = {Min = 1, Max = 1},
        jobs = false,
        neededJobs = false,
        duration = 8000, 
        animDict= 'missmechanic',
        anim = 'work2_base',
        blip = true,
        blipSprite = 403,
        blipColour = 1,
        blipRadius = true
    },
    {
        LabCoords = vector3(4924.8, -5244.45, 2.52),
        neededLabel = '可卡因粉',
        neededLabel1 = '可卡因粉',
        givenLabel = '可卡因',
        neededItem = 'coca',
		neededItem1 = 'coca',
        neededAmount = 2,
        givenItem = 'coke_pooch',
        givenAmount = {Min = 1, Max = 1},
        jobs = false,
        neededJobs = false,
        duration = 6000,
        animDict= 'missmechanic',
        anim = 'work2_base',
        blip = true,
        blipSprite = 497,
        blipColour = 1,
        blipRadius = true
    },
	--[[{
        LabCoords = vector3(3720.03, 4532.75, 21.66),
        neededLabel = '大麻',
		neededLabel1 = '大麻',
        givenLabel = '成品大麻',
        neededItem = 'dama',
		neededItem1 = 'dama',
        neededAmount = 1,
        givenItem = 'chengpindama',
        givenAmount = {Min = 1, Max = 1},
        jobs = false,
        neededJobs = false,
        duration = 8000,
        animDict= 'missmechanic',
        anim = 'work2_base',
        blip = true,
        blipSprite = 496,
        blipColour = 1,
        blipRadius = true
    },]]
}
